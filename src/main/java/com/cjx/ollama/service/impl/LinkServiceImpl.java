package com.cjx.ollama.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjx.ollama.exception.CustomerException;
import com.cjx.ollama.info.PageInfo;
import com.cjx.ollama.mapper.LinkMapper;
import com.cjx.ollama.pojo.dto.BaseQueryDto;
import com.cjx.ollama.pojo.entity.Link;
import com.cjx.ollama.result.PageResult;
import com.cjx.ollama.result.ResultEnum;
import com.cjx.ollama.service.LinkService;
import com.cjx.ollama.utils.page.PageUtil;
import com.cjx.ollama.utils.verify.ValidationUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * Author: cjx
 * Date: 2025/7/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LinkServiceImpl extends ServiceImpl<LinkMapper, Link> implements LinkService {

    private final LinkMapper linkMapper;
//    Spring 通过 AOP 代理来实现事务管理。当你调用一个带 @Transactional 的方法时，实际调用的是代理对象，而不是原始对象
//    使用 ApplicationContext 获取代理对象
//    防止循环依赖
    private final ApplicationContext applicationContext;

    // 获取自身代理对象的方法
    private LinkService getProxySelf() {
        return applicationContext.getBean(LinkService.class);
    }

    @Override
    public PageResult<Link> getLinkList(String keyWord, String category, BaseQueryDto baseQueryDto) {
        // 1. 只构建一次查询条件
        LambdaQueryWrapper<Link> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(keyWord)) {
            queryWrapper.like(Link::getKeyWord, keyWord);
        }
        if (StringUtils.hasText(category)) {
            queryWrapper.eq(Link::getCategory, category);
        }

        // 2. 先查询总数（此时还没有添加分页条件）
        long total = this.count(queryWrapper);//查询可不用getProxySelf()

        // 3. 基于同一个查询条件添加分页参数
        PageInfo pageInfo = PageUtil.addPagination(queryWrapper, baseQueryDto);

        // 4. 查询分页数据（此时查询条件已包含分页）
        List<Link> linkList = this.list(queryWrapper); //查询可不用getProxySelf()

        if (linkList.isEmpty()) {
            log.info("链接列表为空");
        }

        return new PageResult<>(linkList, total, pageInfo.getPageNum(), pageInfo.getPageSize());
    }

    @Override
    public Link insertLink(Link link) {
        link.setCreateTime(LocalDateTime.now());
        link.setUpdateTime(LocalDateTime.now());
        // 调用插入方法，使用代理对象确保事务生效
        boolean isSuccess = getProxySelf().save(link);// 通过代理调用 - 事务生效
        if (isSuccess) {
            log.info("链接插入成功{}", link);
            return link; // 插入成功返回包含自增ID的对象
        } else {
            throw new CustomerException(ResultEnum.LINK_INSERT_FAILED);
        }
    }

    @Override
    public void deleteLink(Long linkId) {
        // 校验 ID
        ValidationUtil.validateLinkId(linkId);
        //调用代理对象的删除方法确保事务生效
        boolean success = getProxySelf().removeById(linkId);// 通过代理调用 - 事务生效
        if (success) {
            log.info("链接删除成功,linkId：{}", linkId);
        } else {
            throw new CustomerException(ResultEnum.LINK_DELETE_FAILED);
        }
    }

    @Override
    public Link updateLink(Link link) {
        // 校验 ID
        ValidationUtil.validateLinkId(link.getLinkId());

        // 查询原数据
        Link existing = linkMapper.selectById(link.getLinkId());
        if (existing == null) {
            throw new CustomerException(ResultEnum.LINK_NOT_FOUND);
        }

        // 比较业务字段是否有变化（注意 null 安全）
        boolean isUnchanged =
                Objects.equals(link.getKeyWord(), existing.getKeyWord()) &&
                        Objects.equals(link.getUrl(), existing.getUrl()) &&
                        Objects.equals(link.getCategory(), existing.getCategory());

        if (isUnchanged) {
            // 业务字段无变化，不需要更新
            throw new CustomerException(ResultEnum.LINK_NOT_MODIFIED);
        }

        // 设置更新时间
        link.setUpdateTime(LocalDateTime.now());

        // 执行更新，使用代理对象确保事务生效
        boolean success = getProxySelf().updateById(link);// 通过代理调用 - 事务生效
        if (!success) {
            throw new CustomerException(ResultEnum.LINK_UPDATE_FAILED);
        }

        return linkMapper.selectById(link.getLinkId());
    }

    @Override
    public List<Link> importLinksByExcel(MultipartFile excel) {
        List<Link> linkList = new ArrayList<>();
        try (InputStream inputStream = excel.getInputStream();
             Workbook workbook = WorkbookFactory.create(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            DataFormatter formatter = new DataFormatter();

            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);

                String keyWord = null;
                String url = null;
                String category = null;

                if (row != null) {
                    keyWord = formatter.formatCellValue(row.getCell(0)).trim();
                    url = formatter.formatCellValue(row.getCell(1)).trim();
                    category = formatter.formatCellValue(row.getCell(2)).trim();
                }

                if (row == null || keyWord.isEmpty() || url.isEmpty()) {
                    continue;
                }

                Link link = new Link();
                link.setKeyWord(keyWord);
                link.setUrl(url);
                link.setCategory(category);
                link.setCreateTime(LocalDateTime.now());
                link.setUpdateTime(LocalDateTime.now());
                linkList.add(link);
            }

            // 获取所有待导入的关键词
            List<String> keyWordsToImport = linkList.stream()
                    .map(Link::getKeyWord)
                    .toList();

            // 如果没有关键词需要导入，直接返回
            if (keyWordsToImport.isEmpty()) {
                log.info("没有需要导入的链接数据");
                return linkList;
            }

            // 构造查询条件，查询已存在的关键词
            LambdaQueryWrapper<Link> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(Link::getKeyWord, keyWordsToImport)
                    .select(Link::getKeyWord); // 只查询关键词字段

            // 查询数据库中已存在的关键词
            List<String> existingKeyWordList = linkMapper.selectList(queryWrapper).stream()
                    .map(Link::getKeyWord)
                    .toList();

            // 过滤掉已存在的关键词
            List<Link> newLinks = linkList.stream()
                    .filter(link -> !existingKeyWordList.contains(link.getKeyWord()))
                    .toList();

            // 统计数量
            int totalCount = linkList.size();
            int importedCount = newLinks.size();
            int skippedCount = totalCount - importedCount;

            log.info("Excel导入统计 - 总记录数: {}, 成功导入: {}, 因关键词已存在跳过: {}",
                    totalCount, importedCount, skippedCount);

            // 通过代理对象调用事务方法导入新链接
            if (!newLinks.isEmpty()) {
                getProxySelf().batchInsertLinks(newLinks);
            }

        } catch (Exception e) {
            log.error("Excel 导入失败：{}", e.getMessage(), e); // 打印完整堆栈信息
            throw new CustomerException(ResultEnum.LINK_IMPORT_FAILED);
        }
        log.info("Excel 导入处理完成");

        return linkList;
    }


    @Override
    public void batchDeleteLinks(List<Long> linkIds) {
        // 1. 校验参数（非空、ID合法性）
        if (linkIds == null || linkIds.isEmpty()) {
            throw new CustomerException(ResultEnum.LINK_ID_EMPTY);
        }
        // 校验每个ID是否合法（复用已有校验逻辑）
        for (Long linkId : linkIds) {
            ValidationUtil.validateLinkId(linkId); // 复用单个ID的校验方法
        }

        // 2. 通过代理对象调用删除方法，确保事务生效
        boolean success = getProxySelf().removeByIds(linkIds); // MyBatis-Plus 批量删除方法

        // 3. 校验删除结果
        if (success) {
            log.info("批量删除成功，linkIds：{}", linkIds);
        } else {
            // 部分ID可能不存在，可调整（如允许部分删除，或严格要求全部存在）
            log.warn("批量删除部分失败，linkIds：{}", linkIds);
            throw new CustomerException(ResultEnum.LINK_BATCH_DELETE_FAILED);
        }
    }

    @Transactional
    public void batchInsertLinks(List<Link> linkList) {
        getProxySelf().saveBatch(linkList); // 事务方法通过代理调用
    }
}