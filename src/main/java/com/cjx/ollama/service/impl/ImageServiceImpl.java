package com.cjx.ollama.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjx.ollama.exception.CustomerException;
import com.cjx.ollama.info.PageInfo;
import com.cjx.ollama.mapper.ImageMapper;
import com.cjx.ollama.pojo.dto.BaseQueryDto;
import com.cjx.ollama.pojo.entity.Image;
import com.cjx.ollama.result.PageResult;
import com.cjx.ollama.result.ResultEnum;
import com.cjx.ollama.service.ImageService;
import com.cjx.ollama.utils.page.PageUtil;
import com.cjx.ollama.utils.verify.ValidationUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.cjx.ollama.utils.constant.Upload.BATCH_IMAGE_UPLOAD_DIR;

/**
 * Author: cjx
 * Date: 2025/7/25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ImageServiceImpl extends ServiceImpl<ImageMapper, Image> implements ImageService {
    private final ImageMapper imageMapper;
    private final ApplicationContext applicationContext;

    // 获取自身代理对象的方法
    private ImageService getProxySelf() {
        return applicationContext.getBean(ImageService.class);
    }

    @Override
    public PageResult<Image> getImageList(String category, String description, BaseQueryDto baseQueryDto) {
        //1.构造查询条件
        LambdaQueryWrapper<Image> queryWrapper = new LambdaQueryWrapper<>();
        //  只有当category不为空且不为空字符串时，才添加该查询条件
        if (StringUtils.hasText(category)) {
            queryWrapper.like(Image::getCategory, category);
        }
        //  只有当description不为空且不为空字符串时，才添加该查询条件
        if (StringUtils.hasText(description)) {
            queryWrapper.like(Image::getDescription, description);
        }
        // 2. 先查询总数（此时还没有添加分页条件）
        long total = this.count(queryWrapper);//查询可不用getProxySelf()
        // 3. 基于同一个查询条件添加分页参数
        PageInfo pageInfo = PageUtil.addPagination(queryWrapper, baseQueryDto);
        // 4. 查询分页数据（此时查询条件已包含分页）
        List<Image> imageList = this.list(queryWrapper);// （查询）：无写操作，无需事务，直接用this.list(queryWrapper)即可，避免代理调用的性能开销
        if (imageList.isEmpty()) {
            log.info("图片列表为空");
        }
        return new PageResult<>(imageList, total, pageInfo.getPageNum(), pageInfo.getPageSize());
    }


    @Override
    public Image insertImage(Image image) {
        image.setCreateTime(LocalDateTime.now());
        image.setUpdateTime(LocalDateTime.now());
        // 调用插入方法，使用代理对象确保事务生效
        boolean isSuccess = getProxySelf().save(image);// 通过代理调用 - 事务生效  如果直接使用 this.save(image)，会导致事务失效
        if (isSuccess) {
            log.info("图片链接插入成功{}", image);
            return image; // 插入成功返回包含自增ID的对象
        } else {
            throw new CustomerException(ResultEnum.IMAGE_INSERT_FAILED);
        }
    }

    /**
     * 删除图片时，需要删除数据库记录和物理文件
     */
    @Override
    public void deleteImage(Long imageId) {
        // 校验 ID
        ValidationUtil.validateImageId(imageId);

        // 在删除前先查询要删除的图片信息（获取URL用于删除文件）
        Image imageToDelete = getProxySelf().getById(imageId);
        if (imageToDelete == null) {
            throw new CustomerException(ResultEnum.IMAGE_NOT_FOUND);
        }

        // 调用代理对象的删除方法确保事务生效
        boolean success = getProxySelf().removeById(imageId);// 通过代理调用 - 事务生效
        if (success) {
            log.info("图片链接删除成功,imageId：{}", imageId);

            // 删除对应的物理文件
            deleteUploadFile(imageToDelete.getUrl());

            log.info("图片删除完成，imageId：{}", imageId);
        } else {
            throw new CustomerException(ResultEnum.IMAGE_DELETE_FAILED);
        }
    }

    @Override
    public Image updateImage(Image image) {
        // 校验 ID
        ValidationUtil.validateImageId(image.getImageId());

        // 查询原数据
        Image existing = imageMapper.selectById(image.getImageId());
        if (existing == null) {
            throw new CustomerException(ResultEnum.IMAGE_NOT_FOUND);
        }

        // 比较业务字段是否有变化（注意 null 安全）
        boolean isUnchanged =
                Objects.equals(image.getUrl(), existing.getUrl()) &&
                        Objects.equals(image.getCategory(), existing.getCategory()) &&
                        Objects.equals(image.getDescription(), existing.getDescription());

        if (isUnchanged) {
            // 业务字段无变化，不需要更新
            throw new CustomerException(ResultEnum.IMAGE_NOT_MODIFIED);
        }

        // 设置更新时间
        image.setUpdateTime(LocalDateTime.now());

        // 执行更新，使用代理对象确保事务生效
        boolean success = getProxySelf().updateById(image);// 通过代理调用 - 事务生效
        if (!success) {
            throw new CustomerException(ResultEnum.IMAGE_UPDATE_FAILED);
        }

        return imageMapper.selectById(image.getImageId());
    }

    /**
     * 文件删除操作放在数据库删除成功之后，避免数据库回滚时文件已被删除的情况
     * 文件删除失败不应该影响数据库操作的成功，所以在 deleteUploadFile 方法中捕获异常但不重新抛出
     */
    @Override
    public void batchDeleteImages(List<Long> imageIds) {
        // 1. 校验参数（非空、ID合法性）
        if (imageIds == null || imageIds.isEmpty()) {
            throw new CustomerException(ResultEnum.IMAGE_ID_EMPTY);
        }
        // 校验每个ID是否合法（复用已有校验逻辑）
        for (Long imageId : imageIds) {
            ValidationUtil.validateImageId(imageId); // 复用单个ID的校验方法
        }

        // 2. 在删除前先查询要删除的图片信息（获取URL用于删除文件）
        List<Image> imagesToDelete = getProxySelf().listByIds(imageIds);

        // 3. 通过代理对象调用删除方法，确保事务生效
        boolean success = getProxySelf().removeByIds(imageIds); // MyBatis-Plus 批量删除方法

        // 4. 校验删除结果
        if (success) {
            log.info("批量删除图片数据库记录成功，imageIds：{}", imageIds);

            // 5. 删除对应的物理文件
            for (Image image : imagesToDelete) {
                deleteUploadFile(image.getUrl());
            }

            log.info("批量删除图片完成，imageIds：{}", imageIds);
        } else {
            // 部分ID可能不存在，可调整（如允许部分删除，或严格要求全部存在）
            log.warn("批量删除图片部分失败，imageIds：{}", imageIds);
            throw new CustomerException(ResultEnum.IMAGE_BATCH_DELETE_FAILED);
        }
    }

    @Override
    public List<Image> importImagesByExcel(MultipartFile excel) {
        List<Image> imageList = new ArrayList<>();
        try (InputStream inputStream = excel.getInputStream();
             Workbook workbook = WorkbookFactory.create(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            DataFormatter formatter = new DataFormatter();

            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);


                String url = null;
                String category = null;
                String description = null;

                if (row != null) {
                    url = formatter.formatCellValue(row.getCell(1)).trim();
                    category = formatter.formatCellValue(row.getCell(2)).trim();
                    description = formatter.formatCellValue(row.getCell(0)).trim();
                }

                if (row == null || description.isEmpty() || url.isEmpty()) {
                    continue;
                }

                Image image = new Image();
                image.setUrl(url);
                image.setCategory(category);
                image.setDescription(description);
                image.setCreateTime(LocalDateTime.now());
                image.setUpdateTime(LocalDateTime.now());
                imageList.add(image);
            }

            // 获取所有待导入的图片描述
            List<String> keyWordsToImport = imageList.stream()
                    .map(Image::getDescription)
                    .toList();

            // 如果没有需要导入，直接返回
            if (keyWordsToImport.isEmpty()) {
                log.info("没有需要导入的图片链接数据");
                return imageList;
            }

            // 构造查询条件，查询已存在的关键词
            LambdaQueryWrapper<Image> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(Image::getDescription, keyWordsToImport)
                    .select(Image::getDescription); // 只查询图片描述字段

            // 查询数据库中已存在的图片描述
            List<String> existingKeyWordList = imageMapper.selectList(queryWrapper).stream()
                    .map(Image::getDescription)
                    .toList();

            // 过滤掉已存在的图片描述
            List<Image> newImages = imageList.stream()
                    .filter(image -> !existingKeyWordList.contains(image.getDescription()))
                    .toList();

            // 统计数量
            int totalCount = imageList.size();
            int importedCount = newImages.size();
            int skippedCount = totalCount - importedCount;

            log.info("Excel导入统计 - 总记录数: {}, 成功导入: {}, 因图片描述已存在跳过: {}",
                    totalCount, importedCount, skippedCount);

            // 通过代理对象调用事务方法导入新链接
            if (!newImages.isEmpty()) {
                getProxySelf().batchInsertImages(newImages);
            }

        } catch (Exception e) {
            log.error("Excel 导入失败：{}", e.getMessage(), e); // 打印完整堆栈信息
            throw new CustomerException(ResultEnum.IMAGE_IMPORT_FAILED);
        }
        log.info("Excel 导入处理完成");

        return imageList;
    }

    /**
     * 事务方法必须是 public
     * 原因：
     * Spring 的事务管理基于 AOP 实现，默认通过动态代理（JDK/CGLIB） JDK 动态代理只能代理接口中的 public 方法；CGLIB 虽然可以代理非 public 方法，但 Spring 为了统一规范和避免潜在问题，强制要求事务方法必须是 public
     * Spring 的事务切面（@Transactional）在内部通过切点表达式匹配目标方法时，默认只拦截 public 方法。如果方法是非 public（如 private、protected、default），切面无法识别该方法，事务注解会失效
     * 非 public 方法在反射或代理调用时会受到访问权限控制，可能导致代理对象无法正确调用目标方法，进而使事务管理失效
     */
    @Transactional
    public void batchInsertImages(List<Image> imageList) {
        //批量插入时，saveBatch默认批次为 1000 条
        getProxySelf().saveBatch(imageList); // 事务方法通过代理调用
    }

    @Override
    public List<String> batchUploadImages( String category, String description, MultipartFile[] files) {
        ValidationUtil.validateImageCategory(category);

        log.info("开始批量上传图片，分类: {}, 数量: {}", category, files.length);
        List<String> fileUrls = new ArrayList<>();

        try {
            String categoryDir = Paths.get(BATCH_IMAGE_UPLOAD_DIR, category).toString();
            ensureDirectoryExist(categoryDir);

            for (MultipartFile file : files) {
                ValidationUtil.validateFileNotEmpty(file);
                ValidationUtil.validateImageFile(file);

                String filename = generateUniqueFilename(file.getOriginalFilename());
                Path filepath = Paths.get(categoryDir, filename);
                Files.write(filepath, file.getBytes());

                String fileUrl = "/" + BATCH_IMAGE_UPLOAD_DIR + category + "/" + filename;
                fileUrls.add(fileUrl);
                log.info("上传成功: {}", fileUrl);

                //批量上传的图片需要保存到数据库
                Image image = new Image();
                image.setUrl(fileUrl);
                image.setCategory(category);
                image.setDescription(description);
                log.info("批量上传图片，description: {}", description);
                image.setCreateTime(LocalDateTime.now());
                image.setUpdateTime(LocalDateTime.now());
                getProxySelf().insertImage(image);
            }

            return fileUrls;

        } catch (CustomerException e) {
            log.error("批量图片上传参数异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("批量图片上传失败", e);
            throw new CustomerException(ResultEnum.FILE_UPLOAD_FAILED);

        }
    }

    /**
     * 确保目录存在
     */
    private void ensureDirectoryExist(
            String directoryPath
    ) {
        File dir = new File(directoryPath);
        if (!dir.exists()) {
            boolean created = dir.mkdirs();
            if (!created) {
                log.error("创建目录失败: {}", directoryPath);
                throw new CustomerException(ResultEnum.DIRECTORY_CREATE_FAILED);
            }
        }
    }

    /**
     * 生成唯一文件名
     */
    private String generateUniqueFilename(
            String originalFilename
    ) {
        //获取文件名称的扩展名
        String ext = StringUtils.getFilenameExtension(originalFilename);
        //生成一个包含当前时间戳和文件扩展名（可选）的字符串
        return System.currentTimeMillis() + (ext != null ? ("." + ext) : "");
    }

    /**
     * 删除物理文件
     * @param fileUrl
     * 文件URL：/image/category/filename
     * 数据库中存储的是 /image/category/filename，需要转换为相对路径 image/category/filename
     * 可能存在数据库记录存在但文件已被手动删除的情况，需要处理（先检查文件是否存在：如果文件存在，正常删除并记录成功日志；如果文件不存在，记录警告日志但不抛出异常，继续处理下一个文件）
     */
    private void deleteUploadFile(String fileUrl) {
        try {
            if (StringUtils.hasText(fileUrl)) {
                // 移除URL前缀的斜杠，转换为相对路径
                String relativePath = fileUrl.startsWith("/") ? fileUrl.substring(1) : fileUrl;
                Path filePath = Paths.get(relativePath);
                // 记录详细信息，便于调试
                log.debug("尝试删除文件: URL={}, 相对路径={}", fileUrl, relativePath);

                if (Files.exists(filePath)) {
                    Files.delete(filePath);
                    log.info("物理文件删除成功: {}", filePath);
                } else {
                    // 判断是否为批量上传的图片，提供更详细的日志信息
                    if (fileUrl.startsWith("/" + BATCH_IMAGE_UPLOAD_DIR)) {
                        log.warn("批量上传的图片文件不存在，可能已被手动删除: {}", filePath);
                    } else {
                        log.warn("物理文件不存在，跳过删除: {}", filePath);
                    }
                }
            }
        } catch (Exception e) {
            log.error("删除物理文件失败: {}, 错误: {}", fileUrl, e.getMessage());
            // 注意：这里不抛出异常，避免因文件删除失败影响数据库操作
        }
    }
}
