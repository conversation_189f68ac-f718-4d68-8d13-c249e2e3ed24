package com.cjx.ollama.utils.export;

import com.cjx.ollama.pojo.entity.ChatMessage;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 导出日志工具类
 * 封装导出成功日志记录逻辑
 */
@Slf4j
public class LogUtil {
    private LogUtil() {
        throw new UnsupportedOperationException("LogUtil工具类不应该被实例化");
    }


    /**
     * 通用导出成功日志方法
     * 增加空值处理，避免参数为null时抛出异常
     *
     * @param fileType     文件类型（不能为空）
     * @param userId       用户 ID（允许为null）
     * @param sessionId    会话 ID（允许为null）
     * @param messages     消息列表（允许为null）
     * @param filePath     文件路径（允许为null）
     */
    public static void logExportSuccess(String fileType, Long userId, String sessionId,
                                        List<ChatMessage> messages, String filePath) {
        // 对关键参数进行空值校验，至少保证日志基本信息可输出
        if (fileType == null) {
            fileType = "未知类型";
        }

        StringBuilder logMsg = new StringBuilder();
        logMsg.append(fileType).append(" 导出成功");

        // 用户ID处理
        if (userId != null) {
            logMsg.append(", userId: ").append(userId);
        } else {
            logMsg.append(", userId: 未知");
        }

        // 会话ID处理
        if (sessionId != null) {
            logMsg.append(", sessionId: ").append(sessionId);
        }

        // 消息数量处理（核心优化点：避免messages为null时调用size()抛出NPE）
        int messageCount = (messages != null) ? messages.size() : 0;
        logMsg.append(", 消息数: ").append(messageCount);

        // 文件路径处理
        if (filePath != null) {
            logMsg.append(", 路径: ").append(filePath);
        }

        log.info(logMsg.toString());
    }
}