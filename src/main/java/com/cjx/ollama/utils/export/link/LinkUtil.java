package com.cjx.ollama.utils.export.link;

import com.cjx.ollama.component.LinkLoader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 关键词链接替换工具类
 * 功能：在文本中自动识别关键词并替换为超链接，支持长关键词优先匹配、避免重复替换
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LinkUtil {

    // 链接加载器，用于获取关键词-链接映射关系
    private final LinkLoader linkLoader;

    // 关键词计数映射，用于统计各关键词出现次数
    private final Map<String, Integer> keywordCountMap = new HashMap<>();

    /**
     * 在内容中插入超链接
     * @param content 原始文本内容
     * @return 处理后的文本（关键词已替换为超链接）
     */
    public String insertLinks(String content) {
        // 空内容直接返回
        if (content == null || content.isEmpty()) return content;

        // 获取预排序的关键词-链接列表（长关键词在前）
        List<Map.Entry<String, String>> entries = linkLoader.getCachedLinkEntries();
        // 查找所有已有<a>标签的位置，避免在已有链接内重复替换
        List<int[]> aTagPositions = findATagPositions(content);
        // 记录已处理的关键词（忽略大小写），确保每个关键词只替换一次
        Set<String> replacedKeywords = new HashSet<>();

        // 用于构建处理结果的字符串缓冲区
        StringBuilder result = new StringBuilder(content.length());
        // 当前处理的字符索引
        int currentIndex = 0;

        // 清空计数映射，准备新的统计
        keywordCountMap.clear();

        // 遍历处理文本中的每个字符
        while (currentIndex < content.length()) {
            boolean handled = false; // 标记当前位置是否已处理

            // 如果当前位置在已有<a>标签内，直接添加字符并移动索引
            if (isInsideATag(currentIndex, aTagPositions)) {
                result.append(content.charAt(currentIndex));
                currentIndex++;
                handled = true;
            }

            // 未处理且不在<a>标签内时，尝试替换关键词
            if (!handled) {
                ReplacementResult replacement = tryReplace(content, currentIndex, entries, replacedKeywords);
                if (replacement != null) {
                    // 追加替换后的内容
                    result.append(replacement.replacement);

                    // 记录关键词出现次数（统一转为小写，避免大小写重复统计）
                    String matchedKeyword = content.substring(
                            currentIndex,
                            currentIndex + replacement.matchedLength
                    ).toLowerCase();
                    keywordCountMap.put(
                            matchedKeyword,
                            keywordCountMap.getOrDefault(matchedKeyword, 0) + 1
                    );

                    // 标记为已替换，避免重复替换
                    replacedKeywords.add(matchedKeyword);
                    // 移动索引到匹配内容的末尾
                    currentIndex += replacement.matchedLength;
                    handled = true;
                }
            }

            // 未匹配到关键词时，直接添加当前字符并移动索引
            if (!handled) {
                result.append(content.charAt(currentIndex));
                currentIndex++;
            }
        }

        // 打印关键词统计信息
        for (Map.Entry<String, Integer> e : keywordCountMap.entrySet()) {
            int length = e.getKey().length();
            int count = e.getValue();
            int totalChars = length * count;
            log.info("关键词: {} 出现次数: {} 字数总计: {}", e.getKey(), count, totalChars);
        }

        return result.toString();
    }

    /**
     * 替换结果封装类
     * 存储替换后的内容和匹配到的关键词长度
     */
    private static class ReplacementResult {
        String replacement; // 替换后的内容
        int matchedLength;  // 匹配到的关键词长度

        ReplacementResult(String replacement, int matchedLength) {
            this.replacement = replacement;
            this.matchedLength = matchedLength;
        }
    }

    /**
     * 尝试在指定位置替换关键词为超链接
     * @param content 原始文本
     * @param index 当前处理位置
     * @param entries 关键词-链接列表（已排序）
     * @param replacedKeywords 已处理的关键词集合
     * @return 替换结果，无匹配则返回null
     */
    private ReplacementResult tryReplace(String content, int index,
                                         List<Map.Entry<String, String>> entries,
                                         Set<String> replacedKeywords) {
        // 遍历关键词（长关键词优先）
        for (Map.Entry<String, String> entry : entries) {
            String keyWord = entry.getKey();

            // 检查当前位置是否有足够长度匹配关键词
            if (index + keyWord.length() <= content.length()) {
                // 提取当前位置与关键词等长的文本
                String matched = content.substring(index, index + keyWord.length());

                // 忽略大小写匹配关键词
                if (matched.equalsIgnoreCase(keyWord)) {
                    String lowerCaseKeyword = keyWord.toLowerCase();

                    // 统计关键词出现次数（无论是否替换）
                    keywordCountMap.put(
                            lowerCaseKeyword,
                            keywordCountMap.getOrDefault(lowerCaseKeyword, 0) + 1
                    );

                    // 仅第一次出现时替换为超链接
                    if (!replacedKeywords.contains(lowerCaseKeyword)) {
                        replacedKeywords.add(lowerCaseKeyword);
                        String link = "<a href='" + entry.getValue() + "'>" + matched + "</a>";
                        return new ReplacementResult(link, keyWord.length());
                    } else {
                        // 后续出现时不替换，保持原文本但跳过已匹配长度
                        return new ReplacementResult(matched, keyWord.length());
                    }
                }
            }
        }
        // 无匹配的关键词
        return null;
    }

    /**
     * 判断当前索引是否在已有<a>标签内
     * @param index 当前字符索引
     * @param aTagPositions <a>标签的位置列表（开始和结束索引）
     * @return true：在<a>标签内；false：不在
     */
    private boolean isInsideATag(int index, List<int[]> aTagPositions) {
        for (int[] pos : aTagPositions) {
            // pos[0]：标签开始索引；pos[1]：标签结束索引
            if (index >= pos[0] && index < pos[1]) {
                return true;
            }
        }
        return false;
    }

    /**
     * 查找文本中所有<a>标签的位置
     * @param content 原始文本
     * @return 包含所有<a>标签起止索引的列表
     */
    private List<int[]> findATagPositions(String content) {
        // 正则表达式：匹配所有<a>标签（忽略大小写）
        final String aTagRegex = "(?i)<a\\b[^>]*>.*?</a>";
        Matcher matcher = Pattern.compile(aTagRegex).matcher(content);
        List<int[]> positions = new ArrayList<>();

        // 记录每个匹配到的<a>标签的起止位置
        while (matcher.find()) {
            positions.add(new int[]{matcher.start(), matcher.end()});
        }
        return positions;
    }
}