package com.cjx.ollama.utils.export;

import com.cjx.ollama.pojo.entity.ChatSession;

import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

import static com.cjx.ollama.service.impl.ExportServiceImpl.CATEGORY_XML_MAP;


/**
 * WordPress XML 生成工具类
 * 封装 WordPress 格式 XML 生成逻辑
 */
public class WordPressXmlUtil {
    private WordPressXmlUtil() {
        throw new UnsupportedOperationException("WordPressXmlUtil工具类不应该被实例化");
    }

    /**
     * 生成 WordPress 格式 XML 内容
     * @param session     会话信息
     * @param title       标题
     * @param content     内容
     * @param postCategory 文章分类
     * @return XML 字符串
     */
    public static String generateWordPressXml(ChatSession session, String title,
                                              String content, String postCategory) {
        // 处理时间格式
        ZonedDateTime sessionCreateTimeZoned = session.getCreateTime().atZone(ZoneId.systemDefault());
        String rfc1123 = DateTimeFormatter.RFC_1123_DATE_TIME.format(sessionCreateTimeZoned);
        String localDate = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(sessionCreateTimeZoned);
        String gmtDate = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(
                sessionCreateTimeZoned.withZoneSameInstant(ZoneOffset.UTC));

        // 生成唯一 ID
        String postId = UUID.randomUUID().toString().replace("-", "").substring(0, 8);

        // 生成分类标签
        String categoryXml = CATEGORY_XML_MAP.getOrDefault(postCategory,
                "<category domain=\"category\" nicename=\"custom\"><![CDATA[" + postCategory + "]]></category>");

        return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<rss version=\"2.0\"\n" +
                "     xmlns:excerpt=\"https://wordpress.org/export/1.2/excerpt/\"\n" +
                "     xmlns:content=\"https://purl.org/rss/1.0/modules/content/\"\n" +
                "     xmlns:wfw=\"https://wellformedweb.org/CommentAPI/\"\n" +
                "     xmlns:dc=\"https://purl.org/dc/elements/1.1/\"\n" +
                "     xmlns:wp=\"https://wordpress.org/export/1.2/\">\n" +
                "  <channel>\n" +
                "    <title>会话导出</title>\n" +
                "    <link>https://127.0.0.1</link>\n" +
                "    <description>自动导出的会话内容</description>\n" +
                "    <pubDate>" + rfc1123 + "</pubDate>\n" +
                "    <language>zh-CN</language>\n" +
                "    <wp:wxr_version>1.2</wp:wxr_version>\n" +
                "    <wp:base_site_url>https://127.0.0.1</wp:base_site_url>\n" +
                "    <wp:base_blog_url>https://127.0.0.1</wp:base_blog_url>\n" +
                "    <wp:author>\n" +
                "      <wp:author_id>1</wp:author_id>\n" +
                "      <wp:author_login>admin</wp:author_login>\n" +
                "      <wp:author_email><EMAIL></wp:author_email>\n" +
                "      <wp:author_display_name><![CDATA[管理员]]></wp:author_display_name>\n" +
                "      <wp:author_first_name><![CDATA[]]></wp:author_first_name>\n" +
                "      <wp:author_last_name><![CDATA[]]></wp:author_last_name>\n" +
                "    </wp:author>\n" +
                "    <item>\n" +
                "      <title><![CDATA[" + title + "]]></title>\n" +
                "      <link>https://127.0.0.1/?p=" + postId + "</link>\n" +
                "      <pubDate>" + rfc1123 + "</pubDate>\n" +
                "      <dc:creator><![CDATA[管理员]]></dc:creator>\n" +
                "      <guid isPermaLink=\"false\">https://127.0.0.1/?p=" + postId + "</guid>\n" +
                "      <description><![CDATA[]]></description>\n" +
                "      <content:encoded><![CDATA[\n" + content + "\n]]></content:encoded>\n" +
                "      <excerpt:encoded><![CDATA[]]></excerpt:encoded>\n" +
                "      <wp:post_id>" + postId + "</wp:post_id>\n" +
                "      <wp:post_date>" + localDate + "</wp:post_date>\n" +
                "      <wp:post_date_gmt>" + gmtDate + "</wp:post_date_gmt>\n" +
                "      <wp:post_modified>" + localDate + "</wp:post_modified>\n" +
                "      <wp:post_modified_gmt>" + gmtDate + "</wp:post_modified_gmt>\n" +
                "      <wp:post_status>publish</wp:post_status>\n" +
                "      <wp:post_type>post</wp:post_type>\n" +
                "      <wp:post_password></wp:post_password>\n" +
                "      <wp:post_name></wp:post_name>\n" +
                "      <wp:status>publish</wp:status>\n" +
                "      <wp:post_parent>0</wp:post_parent>\n" +
                "      <wp:menu_order>0</wp:menu_order>\n" +
                "      <wp:post_mime_type></wp:post_mime_type>\n" +
                "      <wp:comment_status>open</wp:comment_status>\n" +
                "      <wp:pings_status>open</wp:pings_status>\n" +
                "      <wp:post_thumbnail>0</wp:post_thumbnail>\n" +
                categoryXml + "\n" +
                "      <wp:post_meta>\n" +
                "        <wp:meta_key>_edit_last</wp:meta_key>\n" +
                "        <wp:meta_value>1</wp:meta_value>\n" +
                "      </wp:post_meta>\n" +
                "    </item>\n" +
                "  </channel>\n" +
                "</rss>";
    }
}