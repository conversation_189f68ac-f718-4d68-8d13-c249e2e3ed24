package com.cjx.ollama.utils.markdown;

import org.commonmark.parser.Parser;
import org.commonmark.renderer.html.HtmlRenderer;

/**
 * Author: cjx
 * Date: 2025/7/25
 * Markdown 转 HTML 工具类，支持富文本导出
 */
public class MarkdownToHtml {

    private static final Parser PARSER = Parser.builder().build();
    private static final HtmlRenderer RENDERER = HtmlRenderer.builder().build();

    private MarkdownToHtml() {
        throw new UnsupportedOperationException("MarkdownToHtml工具类不应该被实例化");
    }

    public static String markdownToHtml(String markdown) {
        if (markdown == null) return "";
        return RENDERER.render(PARSER.parse(markdown));
    }
}
